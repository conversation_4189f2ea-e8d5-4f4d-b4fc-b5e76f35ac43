import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AudioService } from '../../services/audio.service';

@Component({
  selector: 'app-mute-button',
  standalone: true,
  imports: [CommonModule],
  template: `
    <button class="mute-btn position-fixed top-0 end-0 m-3" (click)="toggleMute()">
      <span *ngIf="audioService.isMuted(); else unmuted">🔇</span>
      <ng-template #unmuted>🔊</ng-template>
    </button>
  `,
  styles: [`
    .mute-btn {
      z-index: 1000;
      background: rgba(27, 20, 100, 0.85);
      border: 2px solid #FFD700;
      color: #FFD700;
      border-radius: 50%;
      width: 44px;
      height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      cursor: pointer;
      transition: background 0.2s, box-shadow 0.2s;
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }
    
    .mute-btn:hover {
      background: #FFD700;
      color: #1B1464;
    }
  `]
})
export class MuteButtonComponent {
  audioService = inject(AudioService);

  toggleMute() {
    this.audioService.toggleMute();
  }
}
