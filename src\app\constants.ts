import { Question, Player, QuestionStatus } from './types';

export const QUIZ_QUESTIONS: Question[] = [
  {
    id: 1,
    question: "A 60-year-old man presents with a wide QRS tachycardia. The ECG shows varying RR intervals, intermittent sinus capture beats, and slurring of the initial QRS in V4–V6 (pseudo-δ waves). Which is the most likely diagnosis?",
    options: ["Atrial fibrillation with pre-excitation", "Supraventricular tachycardia with aberrancy", "Epicardial ventricular tachycardia", "Typical atrial flutter"],
    correctAnswerIndex: 2,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 2,
    question: "A narrow QRS tachycardia shows a ventricular rate of approximately 150 bpm. After IV adenosine administration, transient AV block occurs, revealing sawtooth flutter waves. What is the diagnosis?",
    options: ["AV nodal reentrant tachycardia", "Atrial flutter with 2:1 conduction", "Atrial fibrillation", "Junctional tachycardia"],
    correctAnswerIndex: 1,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 3,
    question: "A 66-year-old man with hypertension and hyperlipidemia presents with sudden onset palpitations. His ECG shows a regular narrow-complex tachycardia at 180 beats per minute. Vagal maneuvers successfully terminate the arrhythmia.",
    options: ["Sinus tachycardia", "Atrioventricular nodal reentrant tachycardia (AVNRT)", "Atrial flutter with 2:1 conduction", "Ventricular tachycardia"],
    correctAnswerIndex: 1,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 4,
    question: "A 71-year-old female with essential thrombocythemia on anagrelide presents with sudden-onset chest pain and palpitations. Her ECG shows a regular wide-complex tachycardia at 150 bpm with right bundle branch block morphology, preceded by visible P waves. Beta-blocker therapy resolves her arrhythmia and symptoms.What is the most likely diagnosis?",
    options: ["Ventricular tachycardia", "Supraventricular tachycardia with aberrant conduction", "Atrial fibrillation with rapid ventricular response", "Sinus tachycardia"],
    correctAnswerIndex: 1,
    status: QuestionStatus.UNANSWERED,
  },
  {
    id: 5,
    question: "A patient presents with episodes of sudden-onset palpitations. The ECG shows a regular narrow-complex tachycardia. Which of the following is the most common underlying mechanism of narrow complex SVT?",
    options: ["Enhanced automaticity from atrial tissue", "Reentry within or around the AV node (AV nodal reentrant tachycardia)", "Accessory pathway-mediated atrioventricular reentrant tachycardia (AVRT)", "Multifocal atrial tachycardia"],
    correctAnswerIndex: 1,
    status: QuestionStatus.UNANSWERED,
  },
  // {
  //   id: 6,
  //   question: "A patient presents with episodes of sudden-onset palpitations. The ECG shows a regular narrow-complex tachycardia. Which of the following is the most common underlying mechanism of narrow complex SVT?",
  //   options: ["Enhanced automaticity from atrial tissue", "Reentry within or around the AV node (AV nodal reentrant tachycardia)", "Liver", "Stomach"],
  //   correctAnswerIndex: 2,
  //   status: QuestionStatus.UNANSWERED,
  // },
  // {
  //   id: 7,
  //   question: "What is the medical term for a 'common cold'?",
  //   options: ["Influenza", "Acute Viral Nasopharyngitis", "Bronchitis", "Pneumonia"],
  //   correctAnswerIndex: 1,
  //   status: QuestionStatus.UNANSWERED,
  // },
  // {
  //   id: 8,
  //   question: "Which of these is NOT a part of the small intestine?",
  //   options: ["Duodenum", "Jejunum", "Ileum", "Cecum"],
  //   correctAnswerIndex: 3,
  //   status: QuestionStatus.UNANSWERED,
  // },
  // {
  //   id: 9,
  //   question: "What is the normal range for human body temperature in Celsius?",
  //   options: ["35.5 - 36.5 °C", "36.5 - 37.5 °C", "37.5 - 38.5 °C", "38.5 - 39.5 °C"],
  //   correctAnswerIndex: 1,
  //   status: QuestionStatus.UNANSWERED,
  // },
  // {
  //   id: 10,
  //   question: "When was the Indian Medical Association founded?",
  //   options: ["1930", "1928", "1905", "1947"],
  //   correctAnswerIndex: 1,
  //   status: QuestionStatus.UNANSWERED,
  // },
];

export const FASTEST_FINGERS: Player[] = [
    { name: "Dr. Priya" },
    { name: "Dr. Pratik" },
    { name: "Dr. Maruti" },
    { name: "Dr. Omkar" },
    { name: "Dr. Ravikant" },
];

export const QUESTION_TIMER_DURATION = 30;
export const RESULTS_VIEW_DURATION = 5;