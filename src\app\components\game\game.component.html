<div class="quiz-container d-flex align-items-center justify-content-center p-4 position-relative">
  <div class="w-100 d-flex gap-4">
    <app-questions-panel [questions]="questions()" [currentQuestionIndex]="currentQuestionIndex()" [score]="score()">
    </app-questions-panel>

    <div class="flex-grow-1 h-100">
      <!-- Winner Celebration Screen -->
      <app-winner-celebration
        *ngIf="gameState() === 2"
        [score]="score()"
        [winnerName]="'KEDAR'">
      </app-winner-celebration>

      <div *ngIf="gameState() !== 2" class="d-flex flex-column h-100 gap-3 ">
        <div class="row h-100 w-100 streamer-chart-container mb-2">
          <div class="col-6">
            <div
              class="w-100 h-100 rounded overflow-hidden shadow-lg position-relative bg-dark d-flex align-items-center justify-content-center">
              <video src="/videos/ECG video.mp4" autoplay muted loop class="streamer w-100 h-100" style="object-fit: cover;"></video>
              <div
                class="position-absolute top-0 end-0 m-2 bg-danger text-white px-3 py-1 small fw-bold rounded d-flex align-items-center gap-2">
                <span>LIVE</span>
                <span class="bg-white rounded-circle live-indicator" style="width: 8px; height: 8px;"></span>
                <span>4,378</span>
              </div>
            </div>
          </div>
          <div class="col-6">
            <app-recent-answers-chart [data]="chartData()"
              [correctAnswerName]="fromCharCode(65 + currentQuestion().correctAnswerIndex) + '. ' + currentQuestion().options[currentQuestion().correctAnswerIndex]">
            </app-recent-answers-chart>
          </div>
        </div>

        <div class="qa-timer-container d-flex flex-column justify-content-center mt-5">
          <!-- Only show timer when not waiting for next question -->
          <div *ngIf="gameState() !== 3" class="d-flex justify-content-center align-items-center mb-4">
            <div [class]="'timer-circle d-flex align-items-center justify-content-center ' +(gameState() === 0 ? 'playing' :(selectedAnswerIndex() === currentQuestion().correctAnswerIndex ? 'correct' : 'incorrect')) +(timer() <= 10 && gameState() === 0 ? ' last-seconds' : '')">
              <p [class.red-text]="timer() <= 10 && gameState() === 0">
                {{ timer() }}
              </p>
            </div>
          </div>

          <!-- Waiting for next question state -->
          <div *ngIf="gameState() === 3" class="flex-grow-1 d-flex flex-column justify-content-center align-items-center gap-4 mt-0">
            <div class="text-center fs-4 fw-bold text-white mb-4">
              <div class="next-question-indicator mb-3">
                <div class="pulse-animation">⏭️</div>
              </div>
              <p class="mb-2">Ready for the next question?</p>
              <p class="fs-5 text-warning">Be Ready....</p>
            </div>
          </div>

          <!-- Normal question state -->
          <div *ngIf="gameState() !== 3" class="flex-grow-1 d-flex flex-column justify-content-center gap-3 mt-0">
            <div class="text-center fs-5 fw-semibold panel-bg text-white py-3 px-2 rounded shadow current-question">
              {{currentQuestion().question}}
            </div>
            <div class="row g-3">
              <div class="col-6" *ngFor="let option of currentQuestion().options; let index = index">
                <button (click)="handleSelectAnswer(index)" [disabled]="gameState() === 1"
                  [class]="getAnswerButtonClass(index)"
                  class="w-100 text-white fw-bold py-3 px-4 rounded shadow answer-btn d-flex align-items-center justify-content-between option-button">
                  <span class="option-text">{{fromCharCode(65 + index)}}. {{option}}</span>
                  <app-check-icon *ngIf="gameState() === 1 && index === currentQuestion().correctAnswerIndex"
                    size="24px">
                  </app-check-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <app-leaderboard [players]="fastestFingers()"></app-leaderboard>
  </div>
</div>