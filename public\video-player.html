<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECG Video Player</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: 100vh;
            object-fit: cover;
        }
        video::-webkit-media-controls {
            display: none !important;
        }
        video::-webkit-media-controls-enclosure {
            display: none !important;
        }
    </style>
</head>
<body>
    <video id="ecgVideo" autoplay muted loop playsinline controls>
        <source src="/videos/ECG video.mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>

    <script>
        const video = document.getElementById('ecgVideo');

        console.log('Video player loaded');

        // Ensure video plays
        video.addEventListener('loadeddata', () => {
            console.log('Video data loaded');
            video.play().catch(error => {
                console.log('Video autoplay failed:', error);
            });
        });

        video.addEventListener('canplay', () => {
            console.log('Video can play');
        });

        video.addEventListener('play', () => {
            console.log('Video started playing, muted:', video.muted);
        });

        // Listen for messages from parent to control video
        window.addEventListener('message', (event) => {
            console.log('Received message:', event.data);

            if (event.data.action === 'unmute') {
                console.log('Attempting to unmute video');
                video.muted = false;
                video.volume = 1.0; // Ensure volume is at maximum

                video.play().then(() => {
                    console.log('Video playing with sound, muted:', video.muted, 'volume:', video.volume);
                }).catch(error => {
                    console.log('Video unmute failed:', error);
                    // Fallback to muted if unmute fails
                    video.muted = true;
                    video.play();
                });
            } else if (event.data.action === 'mute') {
                video.muted = true;
            } else if (event.data.action === 'play') {
                video.play().catch(error => {
                    console.log('Video play failed:', error);
                });
            }
        });

        // Try to play on any user interaction
        document.addEventListener('click', () => {
            console.log('Click detected in iframe');
            if (video.paused) {
                video.play();
            }
            if (video.muted) {
                video.muted = false;
                video.volume = 1.0;
            }
        });

        // Also try on keydown
        document.addEventListener('keydown', (e) => {
            console.log('Key pressed in iframe:', e.key);
            if (video.paused) {
                video.play();
            }
            if (video.muted) {
                video.muted = false;
                video.volume = 1.0;
            }
        });
    </script>
</body>
</html>
