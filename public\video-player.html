<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECG Video Player</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: 100vh;
            object-fit: cover;
        }
    </style>
</head>
<body>
    <video id="ecgVideo" autoplay muted loop playsinline>
        <source src="/videos/ECG video.mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>

    <script>
        const video = document.getElementById('ecgVideo');
        
        // Ensure video plays
        video.addEventListener('loadeddata', () => {
            video.play().catch(error => {
                console.log('Video autoplay failed:', error);
            });
        });

        // Listen for messages from parent to control video
        window.addEventListener('message', (event) => {
            if (event.data.action === 'unmute') {
                video.muted = false;
                video.play().catch(error => {
                    console.log('Video unmute failed:', error);
                    // Fallback to muted if unmute fails
                    video.muted = true;
                    video.play();
                });
            } else if (event.data.action === 'mute') {
                video.muted = true;
            } else if (event.data.action === 'play') {
                video.play().catch(error => {
                    console.log('Video play failed:', error);
                });
            }
        });

        // Try to play on any user interaction with parent
        document.addEventListener('click', () => {
            if (video.paused) {
                video.play();
            }
        });
    </script>
</body>
</html>
