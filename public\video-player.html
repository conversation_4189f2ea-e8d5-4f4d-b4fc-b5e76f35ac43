<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECG Video Player</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #000;
            overflow: hidden;
        }
        video {
            width: 100%;
            height: 100vh;
            object-fit: cover;
        }
        video::-webkit-media-controls {
            display: none !important;
        }
        video::-webkit-media-controls-enclosure {
            display: none !important;
        }
    </style>
</head>
<body>
    <video id="ecgVideo" autoplay loop playsinline controls>
        <source src="/videos/ECG video.mp4" type="video/mp4">
        Your browser does not support the video tag.
    </video>

    <script>
        const video = document.getElementById('ecgVideo');

        console.log('Video player loaded');

        // Set volume to maximum
        video.volume = 1.0;
        video.muted = false; // Start with sound

        // Ensure video plays with sound immediately
        video.addEventListener('loadeddata', () => {
            console.log('Video data loaded');
            video.play().then(() => {
                console.log('Video started playing with sound');
            }).catch(error => {
                console.log('Video autoplay with sound failed, trying muted:', error);
                // Fallback to muted if sound fails initially
                video.muted = true;
                video.play().then(() => {
                    console.log('Video started playing muted');
                    // Try to unmute after a short delay
                    setTimeout(() => {
                        video.muted = false;
                        console.log('Video unmuted after delay');
                    }, 1000);
                });
            });
        });

        video.addEventListener('canplay', () => {
            console.log('Video can play');
        });

        video.addEventListener('play', () => {
            console.log('Video started playing, muted:', video.muted, 'volume:', video.volume);
        });

        // Try to play with sound on any user interaction as fallback
        document.addEventListener('click', () => {
            console.log('Click detected in iframe');
            if (video.paused) {
                video.play();
            }
            if (video.muted) {
                video.muted = false;
                video.volume = 1.0;
            }
        });
    </script>
</body>
</html>
