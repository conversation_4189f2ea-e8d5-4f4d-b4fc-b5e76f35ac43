/* Winner Celebration Screen Styles */
.winner-celebration-bg {
  background: linear-gradient(135deg, #1B1464 0%, #2D1B69 25%, #4A148C 50%, #2D1B69 75%, #1B1464 100%);
  border: 4px solid #FFD700;
  box-shadow: 
    0 0 40px rgba(255, 215, 0, 0.5),
    inset 0 0 20px rgba(255, 215, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.winner-celebration-bg::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 215, 0, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.confetti-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1;
}

.winner-crown {
  z-index: 2;
  position: relative;
}

.crown-icon {
  font-size: 4rem;
  animation: bounce 2s infinite;
  filter: drop-shadow(0 0 10px rgba(255, 215, 0, 0.8));
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20px); }
  60% { transform: translateY(-10px); }
}

.winner-title {
  font-size: 3rem;
  font-weight: 900;
  background: linear-gradient(45deg, #FFD700, #FFA500, #FFD700);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: glow 2s ease-in-out infinite alternate;
  z-index: 2;
  position: relative;
}

@keyframes glow {
  from { filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5)); }
  to { filter: drop-shadow(0 0 20px rgba(255, 215, 0, 0.8)); }
}

.winner-name {
  font-size: 2.5rem;
  font-weight: 800;
  color: #FFD700;
  text-shadow: 
    2px 2px 4px rgba(0, 0, 0, 0.8),
    0 0 10px rgba(255, 215, 0, 0.5);
  letter-spacing: 3px;
  z-index: 2;
  position: relative;
}

.winner-subtitle {
  font-size: 1.5rem;
  font-weight: 600;
  color: #FFA500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  z-index: 2;
  position: relative;
}

.score-display {
  z-index: 2;
  position: relative;
}

.score-label {
  font-size: 1.2rem;
  font-weight: 700;
  color: #FFD700;
  letter-spacing: 2px;
  margin-bottom: 10px;
}

.score-value {
  font-size: 4rem;
  font-weight: 900;
  background: linear-gradient(45deg, #00FF00, #32CD32, #00FF00);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.score-decoration {
  margin-top: 10px;
}

.star {
  font-size: 2rem;
  margin: 0 10px;
  animation: twinkle 1s ease-in-out infinite alternate;
}

.star:nth-child(2) {
  animation-delay: 0.3s;
}

.star:nth-child(3) {
  animation-delay: 0.6s;
}

@keyframes twinkle {
  from { opacity: 0.5; transform: scale(0.8); }
  to { opacity: 1; transform: scale(1.2); }
}

.achievement-badge {
  background: linear-gradient(135deg, #FFD700, #FFA500);
  border-radius: 50px;
  padding: 15px 30px;
  box-shadow: 
    0 8px 16px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(255, 215, 0, 0.4);
  z-index: 2;
  position: relative;
}

.badge-content {
  display: flex;
  align-items: center;
  gap: 15px;
}

.badge-icon {
  font-size: 2rem;
  animation: rotate 3s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.badge-text {
  font-size: 1.3rem;
  font-weight: 800;
  color: #1B1464;
  letter-spacing: 1px;
}

.celebration-message {
  z-index: 2;
  position: relative;
}

.message-text {
  font-size: 1.3rem;
  font-weight: 600;
  color: #FFD700;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  margin-bottom: 8px;
}

.message-subtext {
  font-size: 1.1rem;
  font-weight: 500;
  color: #FFA500;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.7);
  font-style: italic;
}
