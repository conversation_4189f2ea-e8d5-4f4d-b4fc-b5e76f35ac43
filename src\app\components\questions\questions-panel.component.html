<div style="height: 97vh;">
<div style="width: 250px; height: 8%;"> 
   <div class="title w-100 text-center" style="font-size: 1.5rem;">ECGENIUS
</div>  
</div>
<div class="panel-bg rounded-4 shadow-lg p-4 d-flex flex-column text-white" style="width: 250px; height: 90%;">
  <h5 class="text-center fw-bold mb-4 pb-2 border-bottom border-secondary">Questions</h5>
  <p class="text-center mb-4 small">( 10 Points For Each )</p>
  <div #scrollContainer class="flex-grow-1 overflow-auto questions-scroll-container">
    <div class="d-flex flex-column gap-2">
      <button
        *ngFor="let q of questions; let index = index"
        #questionButton
        [class]="getButtonClass(q, index)"
        [disabled]="true">
        Question {{q.id}}
      </button>
    </div>
  </div>
  <div class="mt-4 pt-4 border-top border-secondary">
    <p class="text-center fw-bold fs-4 mb-0">Score: {{score}}</p>
  </div>
</div>
</div>