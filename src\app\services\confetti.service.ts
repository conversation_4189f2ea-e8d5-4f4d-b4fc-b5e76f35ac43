import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class ConfettiService {
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private confetti: any[] = [];
  private animationFrame: number | null = null;
  private isActive = false;

  startConfetti(canvasId: string = 'confetti-canvas') {
    if (this.isActive) return;

    this.canvas = document.getElementById(canvasId) as HTMLCanvasElement;
    if (!this.canvas) return;

    this.ctx = this.canvas.getContext('2d');
    if (!this.ctx) return;

    this.isActive = true;
    this.setupCanvas();
    this.createConfetti();
    this.animate();

    // Auto stop after 5 seconds
    setTimeout(() => {
      this.stopConfetti();
    }, 5000);
  }

  private setupCanvas() {
    if (!this.canvas) return;
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
  }

  private createConfetti() {
    const colors = ['#FFD700', '#FF6B35', '#F7931E', '#FFE135', '#C5E063', '#06FFA5', '#36BCF7', '#8B5CF6', '#F472B6'];
    
    for (let i = 0; i < 150; i++) {
      this.confetti.push({
        x: Math.random() * this.canvas!.width,
        y: Math.random() * this.canvas!.height - this.canvas!.height,
        w: Math.random() * 10 + 5,
        h: Math.random() * 10 + 5,
        color: colors[Math.floor(Math.random() * colors.length)],
        speed: Math.random() * 3 + 2,
        angle: Math.random() * 360,
        rotation: Math.random() * 10 - 5
      });
    }
  }

  private animate = () => {
    if (!this.ctx || !this.canvas || !this.isActive) return;

    this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

    for (let i = this.confetti.length - 1; i >= 0; i--) {
      const c = this.confetti[i];
      
      this.ctx.save();
      this.ctx.translate(c.x + c.w / 2, c.y + c.h / 2);
      this.ctx.rotate((c.angle * Math.PI) / 180);
      this.ctx.fillStyle = c.color;
      this.ctx.fillRect(-c.w / 2, -c.h / 2, c.w, c.h);
      this.ctx.restore();

      c.y += c.speed;
      c.angle += c.rotation;

      if (c.y > this.canvas.height) {
        this.confetti.splice(i, 1);
      }
    }

    // Add new confetti from top
    if (this.confetti.length < 50) {
      const colors = ['#FFD700', '#FF6B35', '#F7931E', '#FFE135', '#C5E063', '#06FFA5', '#36BCF7', '#8B5CF6', '#F472B6'];
      this.confetti.push({
        x: Math.random() * this.canvas.width,
        y: -20,
        w: Math.random() * 10 + 5,
        h: Math.random() * 10 + 5,
        color: colors[Math.floor(Math.random() * colors.length)],
        speed: Math.random() * 3 + 2,
        angle: Math.random() * 360,
        rotation: Math.random() * 10 - 5
      });
    }

    this.animationFrame = requestAnimationFrame(this.animate);
  };

  stopConfetti() {
    this.isActive = false;
    if (this.animationFrame) {
      cancelAnimationFrame(this.animationFrame);
      this.animationFrame = null;
    }
    this.confetti = [];
    if (this.ctx && this.canvas) {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
  }
}
