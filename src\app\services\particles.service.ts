import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class ParticlesService {
  private particlesAnimationFrame: number | null = null;
  private canvas: HTMLCanvasElement | null = null;
  private ctx: CanvasRenderingContext2D | null = null;
  private particles: {x: number, y: number, vx: number, vy: number, r: number}[] = [];
  private isInitialized = false;

  initParticles() {
    if (this.isInitialized) return;

    this.canvas = document.getElementById('global-particles-bg') as HTMLCanvasElement;
    if (!this.canvas) return;
    
    this.ctx = this.canvas.getContext('2d');
    if (!this.ctx) return;

    this.isInitialized = true;
    this.setupCanvas();
    this.createParticles();
    this.animate();
    
    window.addEventListener('resize', this.handleResize);
  }

  private setupCanvas() {
    if (!this.canvas) return;
    
    const width = window.innerWidth;
    const height = window.innerHeight;
    this.canvas.width = width;
    this.canvas.height = height;
  }

  private createParticles() {
    if (!this.canvas) return;
    
    const width = this.canvas.width;
    const height = this.canvas.height;
    const numParticles = 60;
    
    this.particles = [];
    for (let i = 0; i < numParticles; i++) {
      this.particles.push({
        x: Math.random() * width,
        y: Math.random() * height,
        vx: (Math.random() - 0.5) * 0.7,
        vy: (Math.random() - 0.5) * 0.7,
        r: 1.5 + Math.random() * 2.5
      });
    }
  }

  private animate = () => {
    if (!this.ctx || !this.canvas) return;
    
    const width = this.canvas.width;
    const height = this.canvas.height;
    
    this.ctx.clearRect(0, 0, width, height);
    
    for (const p of this.particles) {
      p.x += p.vx;
      p.y += p.vy;
      
      if (p.x < 0 || p.x > width) p.vx *= -1;
      if (p.y < 0 || p.y > height) p.vy *= -1;
      
      this.ctx.beginPath();
      this.ctx.arc(p.x, p.y, p.r, 0, 2 * Math.PI);
      this.ctx.fillStyle = 'rgba(255,255,255,0.7)';
      this.ctx.fill();
    }
    
    this.particlesAnimationFrame = requestAnimationFrame(this.animate);
  };

  private handleResize = () => {
    if (!this.canvas) return;
    
    this.setupCanvas();
    this.createParticles();
  };

  destroyParticles() {
    if (this.particlesAnimationFrame) {
      cancelAnimationFrame(this.particlesAnimationFrame);
      this.particlesAnimationFrame = null;
    }
    
    window.removeEventListener('resize', this.handleResize);
    
    if (this.canvas && this.ctx) {
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
    }
    
    this.canvas = null;
    this.ctx = null;
    this.particles = [];
    this.isInitialized = false;
  }
}
