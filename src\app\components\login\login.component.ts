import { Component, inject } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AudioPlayerComponent } from '../audio-player/audio-player.component';
import { AudioService } from '../../services/audio.service';

@Component({
  selector: 'app-login',
  standalone: true,
  imports: [CommonModule, AudioPlayerComponent],
  templateUrl: './login.component.html',
  styleUrl: './login.component.css'
})
export class LoginComponent {
  email = '';
  emailError = '';
  private router = inject(Router);
  audioService = inject(AudioService); 
  handleStart() {
    if (!this.email || !/^\S+@\S+\.\S+$/.test(this.email)) {
      this.emailError = 'Please enter a valid email address.';
      return;
    }
    this.emailError = '';
    this.router.navigate(['/game']);
  }
  onEmailChange(value: string) {
    this.email = value;
    if (this.emailError) {
      this.emailError = '';
    }
  }
}
