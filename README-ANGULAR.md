# Live Medical Quiz - Angular Version

This is the Angular version of the Live Medical Quiz application, converted from the original React version.

## Features

- Interactive medical quiz with 10 questions
- Real-time timer for each question
- Score tracking
- Visual feedback for correct/incorrect answers
- Chart display showing answer distribution
- Responsive design using Bootstrap

## Technologies Used

- Angular 20.1.0 (with standalone components and signals)
- Bootstrap 5 for styling
- Chart.js for data visualization
- TypeScript

## Project Structure

```
src/
├── app/
│   ├── components/
│   │   ├── check-icon.component.ts
│   │   ├── leaderboard.component.ts
│   │   ├── questions-panel.component.ts
│   │   └── recent-answers-chart.component.ts
│   ├── app.ts (main component)
│   ├── app.html (main template)
│   ├── app.css (component styles)
│   ├── constants.ts (quiz data)
│   └── types.ts (TypeScript interfaces)
├── styles.css (global styles)
└── main.ts (bootstrap file)
```

## Installation & Setup

1. Navigate to the project directory:
   ```bash
   cd live-medical-quiz-angular/live-medical-quiz-app
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Start the development server:
   ```bash
   ng serve
   ```
   or
   ```bash
   npm start
   ```

4. Open your browser and navigate to `http://localhost:4200`

## Key Differences from React Version

1. **State Management**: Uses Angular Signals instead of React hooks
2. **Components**: Standalone Angular components instead of React functional components
3. **Styling**: Bootstrap classes instead of Tailwind CSS
4. **Data Binding**: Angular template syntax with property and event binding
5. **Lifecycle**: Angular lifecycle hooks (ngOnInit, ngOnDestroy) instead of useEffect

## Components

### App Component
- Main application component managing game state
- Handles email validation, timer, and game flow
- Uses Angular Signals for reactive state management

### QuestionsPanel Component
- Displays list of questions with status indicators
- Auto-scrolls to current question
- Shows current score

### Leaderboard Component
- Shows fastest fingers leaderboard
- Static list of top players

### RecentAnswersChart Component
- Displays bar chart of answer distribution
- Uses Chart.js for visualization
- Shows correct answer in green, others in red

### CheckIcon Component
- SVG check mark icon
- Used to indicate correct answers

## Game Flow

1. User enters email address
2. Quiz starts with 15-second timer per question
3. User selects answer or timer expires
4. Chart shows answer distribution for 5 seconds
5. Proceeds to next question
6. Final score displayed at the end

## Customization

- Questions can be modified in `constants.ts`
- Timer durations can be adjusted in `constants.ts`
- Styling can be customized in `styles.css` and component templates
- Chart colors and behavior can be modified in `recent-answers-chart.component.ts`

## Build for Production

```bash
ng build --prod
```

The built files will be in the `dist/` directory.

## Notes

- Images should be placed in `public/images/logos/` directory
- The application uses a gradient background as fallback
- Bootstrap is included via npm and imported in styles.css
- Chart.js is used for data visualization instead of Recharts