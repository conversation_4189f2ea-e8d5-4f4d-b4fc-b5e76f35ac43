@import 'bootstrap/dist/css/bootstrap.min.css';

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #475569 #1e293b;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #1e293b;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Global styles */
body {
  margin: 0;
  padding: 0;
  font-family: 'Orbitron', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: url('/images/logos/kbc-background-image.png') no-repeat center center fixed;
  background-size: cover;
}

/* Custom Quiz Styles */
.quiz-container {
  height: 99.99vh;
  background: rgba(0, 0, 0, 0.3);
}

.panel-bg {
  background: rgba(30, 41, 59, 0.5);
  backdrop-filter: blur(16px);
  border: 1px solid rgba(100, 116, 139, 0.5);
}



.question-btn {
  transition: all 0.3s ease;
  transform: scale(1);
}

.question-btn:hover {
  transform: scale(1.05);
}

.question-btn.correct {
  background-color: rgba(34, 197, 94, 0.8);
  border-color: #4ade80;
}

.question-btn.incorrect {
  background-color: rgba(239, 68, 68, 0.8);
  border-color: #f87171;
}

.question-btn.current {
  background-color: rgba(59, 130, 246, 0.8);
  border-color: #60a5fa;
  animation: pulse 2s infinite;
}



.live-indicator {
  animation: pulse 2s infinite;
}

.game-over-bg {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(16px);
}



@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

.title {
  font-size: 2.2rem;
  font-weight: 700;
  color: #FFD700; 
  text-align: center;
  margin-bottom: 1.5rem;
  border-bottom: 2px solid #FFD700;
  display: inline-block;
  padding-bottom: 6px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
  letter-spacing: 1px;
}


.ecg-input {
  background-color: #0B0B45; /* Deep navy blue */
  color: #FFD700;            /* Gold text */
  border: 2px solid #FFD700;
  padding: 12px 18px;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 10px;
  outline: none;
  width: 100%;
  max-width: 400px;
  transition: all 0.3s ease;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
  letter-spacing: 1px;
}

.ecg-input::placeholder {
  color: #C0C0C0; /* Silver placeholder */
  opacity: 0.7;
}

.ecg-input:focus {
  border-color: #FFD700;
  box-shadow: 0 0 20px #FFD700, 0 0 30px #1B1464;
  background-color: #1B1464;
  color: #FFD700;
}


.ecg-btn {
  background-color: #1B1464; /* Deep Royal Blue */
  color: #FFD700;            /* Gold Text */
  border: 2px solid #FFD700;
  padding: 12px 28px;
  font-size: 1.1rem;
  font-weight: bold;
  border-radius: 50px;
  cursor: pointer;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  box-shadow: 0 0 10px #FFD700, 0 0 20px #1B1464;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

.ecg-btn::before {
  content: '';
  position: absolute;
  background: rgba(255, 215, 0, 0.2);
  top: 0;
  left: -75%;
  width: 50%;
  height: 100%;
  transform: skewX(-25deg);
  transition: 0.5s;
  z-index: 0;
}

.ecg-btn:hover::before {
  left: 130%;
}

.ecg-btn:hover {
  background-color: #FFD700;
  color: #1B1464;
  box-shadow: 0 0 20px #FFD700, 0 0 40px #FFD700;
  border-color: #FFD700;
}


