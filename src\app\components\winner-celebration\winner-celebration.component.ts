import { Component, Input, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ConfettiService } from '../../services/confetti.service';

@Component({
  selector: 'app-winner-celebration',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './winner-celebration.component.html',
  styleUrl: './winner-celebration.component.css'
})
export class WinnerCelebrationComponent implements OnInit, OnDestroy {
  @Input() score: number = 0;
  @Input() winnerName: string = 'KEDAR';

  private confettiService = inject(ConfettiService);

  ngOnInit() {
    // Start confetti celebration after a short delay
    setTimeout(() => {
      this.confettiService.startConfetti('winner-confetti');
    }, 500);
  }

  ngOnDestroy() {
    this.confettiService.stopConfetti();
  }
}
