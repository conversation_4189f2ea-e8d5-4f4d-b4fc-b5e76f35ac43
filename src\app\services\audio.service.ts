import { Injectable, signal, effect } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class AudioService {
  private audioMap: Map<string, HTMLAudioElement> = new Map();
  private volumeMap: Map<string, number> = new Map(); // Store original volumes

  // Global mute state with persistence
  isMuted = signal<boolean>(this.getStoredMuteState());

  constructor() {
    // Watch for mute state changes and persist them
    effect(() => {
      const muted = this.isMuted();
      localStorage.setItem('audioMuted', JSON.stringify(muted));
      this.applyMuteState(muted);
    });
  }

  private getStoredMuteState(): boolean {
    try {
      const stored = localStorage.getItem('audioMuted');
      return stored ? JSON.parse(stored) : false;
    } catch {
      return false;
    }
  }

  private applyMuteState(muted: boolean) {
    this.audioMap.forEach((audio, src) => {
      if (muted) {
        // Store original volume before muting
        if (!this.volumeMap.has(src)) {
          this.volumeMap.set(src, audio.volume);
        }
        audio.volume = 0;
        audio.pause(); // Also pause the audio
        audio.currentTime = 0;
      } else {
        // Restore original volume when unmuting
        const originalVolume = this.volumeMap.get(src) || 1;
        audio.volume = originalVolume;
      }
    });
  }

  async play(src: string, loop: boolean = false) {
    try {
      let audio = this.audioMap.get(src);
      if (!audio) {
        audio = new Audio(src);
        audio.volume = 1; // Set default volume
        this.audioMap.set(src, audio);
        this.volumeMap.set(src, 1); // Store default volume
      }

      audio.loop = loop;
      audio.currentTime = 0;

      // If muted, set volume to 0 but don't prevent audio creation
      if (this.isMuted()) {
        audio.volume = 0;
        return; // Don't play when muted
      }

      await audio.play();
    } catch (error) {
      console.warn('Audio playback failed:', error);
    }
  }

  stop(src?: string) {
    if (src) {
      const audio = this.audioMap.get(src);
      if (audio) {
        audio.pause();
        audio.currentTime = 0;
      }
    } else {
      this.stopAll();
    }
  }

  private stopAll() {
    this.audioMap.forEach((audio) => {
      audio.pause();
      audio.currentTime = 0;
    });
  }

  pause(src?: string) {
    const audio = src ? this.audioMap.get(src) : null;
    if (audio) {
      audio.pause();
    }
  }

  setVolume(src: string, volume: number) {
    const audio = this.audioMap.get(src);
    if (audio) {
      // Store the intended volume
      this.volumeMap.set(src, volume);

      // Only apply volume if not muted
      if (!this.isMuted()) {
        audio.volume = volume;
      }
    }
  }

  toggleMute() {
    this.isMuted.set(!this.isMuted());
  }

  // Method to get all currently playing audio elements
  private getCurrentlyPlayingAudio(): HTMLAudioElement[] {
    const playingAudio: HTMLAudioElement[] = [];
    this.audioMap.forEach((audio) => {
      if (!audio.paused) {
        playingAudio.push(audio);
      }
    });
    return playingAudio;
  }

  // Method to check if any audio is currently playing
  isAnyAudioPlaying(): boolean {
    return this.getCurrentlyPlayingAudio().length > 0;
  }
}
