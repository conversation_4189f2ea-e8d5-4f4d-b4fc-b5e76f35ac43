<app-audio-player [src]="'/audios/bg.mp3'" [loop]="true"></app-audio-player>
<!-- Start Screen (moved from AppComponent) -->
<div class="min-vh-100 d-flex flex-column align-items-center justify-content-center p-4">
  <main class="w-100">
    <div class="text-center">
      <h1 class="fw-bold mb-4 title">ECGENIUS
</h1>
      <div class="d-flex flex-column align-items-center gap-3">
        <input style="max-width: 30vw;" type="email" [value]="email" (input)="onEmailChange($any($event.target).value)"
          placeholder="Enter Your Mail" class="form-control form-control-lg ecg-input" aria-label="Enter Your Mail"
          required />
        <button type="submit" (click)="handleStart()" class="btn btn-secondary btn-lg px-4 ecg-btn mt-3">
          Start
        </button>
        <p *ngIf="emailError" class="text-danger mt-2 small">{{emailError}}</p>
      </div>
    </div>
  </main>
  <footer class="position-absolute bottom-0 start-50 translate-middle-x text-center w-100 pb-4">
    <p class="text-white" style="font-size: 1.2rem;">Knowledge Partner</p>
    <div class="d-flex justify-content-center">
      <div class="bg-light rounded p-1">
        <img width="250" src="images/logos/Mediccapress-partner.png" alt="" srcset="">
      </div>
    </div>
  </footer>
</div>