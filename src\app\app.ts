import { <PERSON>mpo<PERSON>, OnIni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, inject } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { MuteButtonComponent } from './components/mute-button/mute-button.component';
import { ParticlesService } from './services/particles.service';

@Component({
  selector: 'app-root',
  imports: [RouterOutlet, MuteButtonComponent],
  templateUrl: './app.html',
  styleUrl: './app.css'
})
export class App implements OnInit, OnDestroy {
  private particlesService = inject(ParticlesService);

  ngOnInit() {
    // Initialize global particles after view is ready
    setTimeout(() => {
      this.particlesService.initParticles();
    }, 0);
  }

  ngOnDestroy() {
    this.particlesService.destroyParticles();
  }
}
