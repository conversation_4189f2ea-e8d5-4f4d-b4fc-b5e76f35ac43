/*  */import { Component, Input } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Player } from '../../types';

@Component({
  selector: 'app-leaderboard',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './leaderboard.component.html',
  styleUrl: './leaderboard.component.css'
})
export class LeaderboardComponent {
  @Input() players: Player[] = [];

  leaderboardEntries = [
    { rank: 1, name: '<PERSON>' },
    { rank: 2, name: '<PERSON>' },
    { rank: 3, name: '<PERSON>' },
    { rank: 4, name: '<PERSON>' },
    { rank: 5, name: '<PERSON>' },
  ];
}