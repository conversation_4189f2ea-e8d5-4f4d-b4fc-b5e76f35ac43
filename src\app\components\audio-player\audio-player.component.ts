import { Component, Input, OnInit, OnD<PERSON>roy, inject, effect } from '@angular/core';
import { AudioService } from '../../services/audio.service';

@Component({
  selector: 'app-audio-player',
  standalone: true,
  template: '',
})
export class AudioPlayerComponent implements OnInit, OnDestroy {
  @Input() src: string = '';
  @Input() loop: boolean = false;

  private audioService = inject(AudioService);

  constructor() {
    // Set up effect to handle mute state changes - must be in constructor for injection context
    effect(() => {
      const isMuted = this.audioService.isMuted();
      if (this.src) {
        if (isMuted) {
          this.audioService.stop(this.src);
        } else {
          this.audioService.play(this.src, this.loop);
        }
      }
    });
  }

  ngOnInit() {
    if (this.src) {
      this.audioService.play(this.src, this.loop);
    }
  }

  ngOnDestroy() {
    if (this.src) {
      this.audioService.stop(this.src);
    }
  }
}
