/* Ensure both sections take exactly 50% of available space */
.fastest-fingers-section,
.leaderboard-section {
  flex: 1 1 50%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

/* Ensure the lists can scroll independently */
.fastest-fingers-section ul,
.leaderboard-section ul {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
}

/* Custom scrollbar styling */
.fastest-fingers-section ul::-webkit-scrollbar,
.leaderboard-section ul::-webkit-scrollbar {
  width: 6px;
}

.fastest-fingers-section ul::-webkit-scrollbar-track,
.leaderboard-section ul::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.fastest-fingers-section ul::-webkit-scrollbar-thumb,
.leaderboard-section ul::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.6);
  border-radius: 3px;
}

.fastest-fingers-section ul::-webkit-scrollbar-thumb:hover,
.leaderboard-section ul::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.8);
}

/* Ensure consistent spacing */
.fastest-fingers-section h2,
.leaderboard-section h3 {
  flex-shrink: 0;
  margin-bottom: 1rem;
}

/* List item styling */
.fastest-fingers-section li,
.leaderboard-section li {
  flex-shrink: 0;
  transition: background-color 0.2s ease;
}

.fastest-fingers-section li:hover,
.leaderboard-section li:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}
