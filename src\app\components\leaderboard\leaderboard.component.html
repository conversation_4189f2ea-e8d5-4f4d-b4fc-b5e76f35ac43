<div style="height: 97vh;">
<div style="width: 250px; height: 8%;"> 
   <div class="title w-100 text-center" style="font-size: 1.5rem;">Kedar </div>  
</div>
<div class="panel-bg rounded-4 shadow-lg p-4 d-flex flex-column text-white" style="width: 250px; height: 90%;">
    <!-- Fastest Fingers Section - 50% -->
    <div class="fastest-fingers-section">
      <h5 class="text-center fw-bold mb-3 pb-2 border-bottom border-secondary">Fastest Fingers</h5>
      <ul class="list-unstyled d-flex flex-column gap-2">
        <li *ngFor="let player of players; let index = index"
            class="bg-secondary bg-opacity-75 rounded p-2 fw-medium shadow d-flex align-items-center">
          <span class="me-3 fw-bold">{{index + 1}}.</span>
          {{player.name}}
        </li>
      </ul>
    </div>

    <!-- Leaderboard Section - 50% -->
    <div class="leaderboard-section mt-3">
      <h5 class="text-center fw-bold mb-3 pb-2 border-bottom border-secondary">Leaderboard</h5>
      <ul class="list-unstyled d-flex flex-column gap-2">
        <li *ngFor="let entry of leaderboardEntries"
            class="bg-secondary bg-opacity-75 rounded p-2 fw-medium shadow d-flex align-items-center">
          <span class="me-3 fw-bold">{{entry.rank}}.</span>
          <span class="flex-grow-1">{{entry.name}}</span>
        </li>
      </ul>
    </div>
</div>