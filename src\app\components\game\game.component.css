.streamer {
    object-fit: contain;
    border-radius: 12px;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.2);
}

.streamer-chart-container{
    height: 40vh;
}

.qa-timer-container{
    height: 40vh;
}

.option-button {
    min-height: 70px;
    height: auto;
    padding: 0.75rem 1rem !important;
}

.option-text {
    line-height: 1.3;
    word-wrap: break-word;
    overflow-wrap: break-word;
    text-align: left;
    flex-grow: 1;
}

.timer-circle {
  width: 90px;
  height: 90px;
  background: radial-gradient(circle at center, #1B1464, #0B0B45);
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.2), 0 0 10px rgba(27, 20, 100, 0.4);
  border-radius: 50%;
  border: 4px solid transparent;
  transition: all 0.4s ease-in-out;
  position: relative;
  animation: pulse-ring 1s infinite;
}

.timer-circle.playing {
  border-color: #FFD700;
  box-shadow: 0 0 12px #FFD700, 0 0 24px #FFD700, inset 0 0 10px #FFD700;
}

.timer-circle.correct {
  border-color: #22c55e;
  box-shadow: 0 0 12px #22c55e, 0 0 24px #22c55e, inset 0 0 10px #22c55e;
  animation: pulse-ring-green 1s infinite;
}

@keyframes pulse-ring-green {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.08);
  }
  100% {
    transform: scale(1);
  }
}

.timer-circle.incorrect {
  animation: pulse-ring-red 1s infinite;
  border-color: #dc2626;
  box-shadow: 0 0 12px #dc2626, 0 0 24px #dc2626, inset 0 0 10px #dc2626;
}

.timer-circle p {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
  z-index: 1;
  color: #FFD700;
  transition: color 0.3s ease-in-out;
}

.timer-circle p.red-text {
  color: #ff4d4d; 
}

.timer-circle.last-seconds {
  border-color: #ff4d4d;
  box-shadow: 0 0 12px #ff4d4d, 0 0 24px #ff4d4d, inset 0 0 10px #ff4d4d;
  animation: pulse-ring-red 1s infinite;
}

@keyframes pulse-ring-red {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.08);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.08);
  }
  100% {
    transform: scale(1);
  }
}

                                       

.current-question {
  background: linear-gradient(135deg, #1B1464, #0B0B45); /* Royal blue gradient */
  border: 2px solid #FFD700; /* Golden border */
  color: #FFD700;
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  text-align: center;
  padding: 1rem 1.5rem;
  border-radius: 12px;
  box-shadow:
    0 0 10px rgba(255, 215, 0, 0.4),
    0 0 20px rgba(27, 20, 100, 0.5);
  transition: all 0.3s ease;
  letter-spacing: 0.3px;
  position: relative;
  z-index: 1;
  line-height: 1.4;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.current-question:hover {
  box-shadow: 
    0 0 15px #FFD700,
    0 0 30px rgba(255, 215, 0, 0.8);
}

.answer-btn {
  background: linear-gradient(135deg, #1B1464, #0B0B45);
  border: 2px solid #FFD700;
  color: #FFD700;
  font-size: 0.95rem;
  font-weight: 600;
  letter-spacing: 0.3px;
  transition: all 0.3s ease;
  font-family: 'Poppins', sans-serif;
  position: relative;
  overflow: hidden;
}

.answer-btn:hover:not(:disabled) {
  box-shadow: 0 0 10px #FFD700, 0 0 20px #FFD700;
  background: linear-gradient(135deg, #0B0B45, #1B1464);
}

.answer-btn:disabled {
  opacity: 0.9;
  cursor: not-allowed;
}

.answer-btn.correct {
  background: linear-gradient(135deg, #14532d, #166534);
  border-color: #22c55e;
  color: #22c55e;
  box-shadow: 0 0 15px #22c55e, inset 0 0 10px #22c55e;
}

.answer-btn.incorrect {
  background: linear-gradient(135deg, #7f1d1d, #991b1b); /* Red tones */
  border-color: #dc2626;
  color: #dc2626;
  box-shadow: 0 0 15px #dc2626, inset 0 0 10px #dc2626;
}

.answer-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -75%;
  width: 50%;
  height: 100%;
  background: rgba(255, 215, 0, 0.1);
  transform: skewX(-25deg);
  transition: 0.5s;
  z-index: 0;
}

.answer-btn:hover::before {
  left: 130%;
}



.quiz-container {
  position: relative;
  z-index: 1;
}

/* Mute button styles */
.mute-btn {
  z-index: 10;
  background: rgba(27, 20, 100, 0.85);
  border: 2px solid #FFD700;
  color: #FFD700;
  border-radius: 50%;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  transition: background 0.2s, box-shadow 0.2s;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}
.mute-btn:hover {
  background: #FFD700;
  color: #1B1464;
}

/* Waiting for next question styles */
.next-question-indicator {
  display: flex;
  justify-content: center;
  align-items: center;
}

.pulse-animation {
  font-size: 3rem;
  animation: pulse-glow 1.5s ease-in-out infinite;
}

@keyframes pulse-glow {
  0%, 100% {
    transform: scale(1);
    filter: drop-shadow(0 0 5px rgba(255, 215, 0, 0.5));
  }
  50% {
    transform: scale(1.2);
    filter: drop-shadow(0 0 15px rgba(255, 215, 0, 0.8));
  }
}


