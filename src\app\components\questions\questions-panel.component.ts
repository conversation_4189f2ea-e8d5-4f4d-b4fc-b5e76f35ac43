import { Component, Input, OnChanges, SimpleChanges, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Question, QuestionStatus } from '../../types';

@Component({
  selector: 'app-questions-panel',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './questions-panel.component.html',
  styleUrl: './questions-panel.component.css'
})
export class QuestionsPanelComponent implements OnChanges, AfterViewInit {
  @Input() questions: Question[] = [];
  @Input() currentQuestionIndex: number = 0;
  @Input() score: number = 0;

  @ViewChild('scrollContainer') scrollContainer!: ElementRef;

  ngAfterViewInit() {
    // Initial scroll to top (Question 1)
    if (this.scrollContainer) {
      this.scrollContainer.nativeElement.scrollTop = 0;
    }
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['currentQuestionIndex'] && this.scrollContainer) {
      // Auto-scroll to current question
      setTimeout(() => {
        const currentButton = this.scrollContainer.nativeElement.querySelector(`button:nth-child(${this.currentQuestionIndex + 1})`);
        if (currentButton) {
          currentButton.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          });
        }
      });
    }
  }

  getButtonClass(question: Question, index: number): string {
    let baseClass = "btn w-100 text-white fw-semibold py-2 px-3 rounded shadow question-btn border-2";
    
    if (question.status === QuestionStatus.CORRECT) {
      return `${baseClass} correct`;
    }
    if (question.status === QuestionStatus.INCORRECT) {
      return `${baseClass} incorrect`;
    }
    if (index === this.currentQuestionIndex) {
      return `${baseClass} current`;
    }
    return `${baseClass} btn-secondary`;
  }
}