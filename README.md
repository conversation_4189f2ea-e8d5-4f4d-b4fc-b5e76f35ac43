# Live Medical Quiz - Angular Application

A modern, interactive medical quiz application built with Angular 20, featuring real-time scoring, visual feedback, and responsive design.

## 🚀 Features

- **Interactive Quiz**: 10 medical questions with multiple choice answers
- **Real-time Timer**: 15 seconds per question with visual countdown
- **Score Tracking**: 10 points per correct answer
- **Visual Feedback**: Color-coded answers (green for correct, red for incorrect)
- **Chart Visualization**: Bar chart showing answer distribution after each question
- **Responsive Design**: Bootstrap-powered responsive UI
- **Modern Angular**: Built with Angular 20 using standalone components and signals

## 📁 Project Structure

```
src/
├── app/
│   ├── components/
│   │   ├── check-icon.component.ts          # SVG check mark icon
│   │   ├── leaderboard.component.ts         # Fastest fingers leaderboard
│   │   ├── questions-panel.component.ts     # Questions list with status
│   │   └── recent-answers-chart.component.ts # Chart.js bar chart
│   ├── app.config.ts                        # Application configuration
│   ├── app.css                              # Component-specific styles
│   ├── app.html                             # Main template
│   ├── app.ts                               # Main application component
│   ├── constants.ts                         # Quiz questions and settings
│   └── types.ts                             # TypeScript interfaces
├── index.html                               # HTML entry point
├── main.ts                                  # Application bootstrap
└── styles.css                               # Global styles
```

## 🛠️ Technologies Used

- **Angular 20.1.0** - Latest Angular with standalone components
- **Angular Signals** - Reactive state management
- **Bootstrap 5.3.3** - CSS framework for responsive design
- **Chart.js 4.4.6** - Data visualization library
- **TypeScript 5.8.2** - Type-safe JavaScript

## 📦 Installation & Setup

1. **Clone or download the project**
   ```bash
   cd live-medical-quiz-angular/live-medical-quiz-app
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Start development server**
   ```bash
   npm start
   ```
   or
   ```bash
   ng serve
   ```

4. **Open in browser**
   Navigate to `http://localhost:4200`

## 🎮 How to Play

1. **Enter Email**: Provide a valid email address to start
2. **Answer Questions**: Select from 4 multiple choice options
3. **Beat the Timer**: Each question has a 15-second countdown
4. **View Results**: See answer distribution chart after each question
5. **Track Progress**: Monitor your score and question status in the sidebar
6. **Final Score**: View your total score at the end

## 🏗️ Build for Production

```bash
npm run build
```

The build artifacts will be stored in the `dist/` directory.

## 🎨 Customization

### Questions
Edit `src/app/constants.ts` to modify:
- Quiz questions and answers
- Timer durations
- Player leaderboard

### Styling
- **Global styles**: `src/styles.css`
- **Component styles**: `src/app/app.css`
- **Bootstrap customization**: Modify Bootstrap variables in `styles.css`

### Chart Configuration
Customize chart appearance in `src/app/components/recent-answers-chart.component.ts`

## 🔧 Key Components

### App Component (`app.ts`)
- Main application logic and state management
- Uses Angular Signals for reactive updates
- Handles game flow, timer, and scoring

### Questions Panel (`questions-panel.component.ts`)
- Displays all questions with status indicators
- Auto-scrolls to current question
- Shows real-time score

### Leaderboard (`leaderboard.component.ts`)
- Shows fastest fingers ranking
- Static display of top players

### Recent Answers Chart (`recent-answers-chart.component.ts`)
- Bar chart visualization using Chart.js
- Shows answer distribution percentages
- Highlights correct answer in green

### Check Icon (`check-icon.component.ts`)
- Reusable SVG check mark component
- Indicates correct answers

## 🎯 Game Logic

- **Timer**: 15 seconds per question, 5 seconds to view results
- **Scoring**: 10 points per correct answer
- **Question Flow**: Linear progression through all questions
- **Answer Feedback**: Immediate visual feedback with colors
- **Chart Data**: Randomized percentage distribution for demonstration

## 📱 Responsive Design

The application is fully responsive using Bootstrap 5:
- **Mobile**: Stacked layout with touch-friendly buttons
- **Tablet**: Optimized spacing and component sizing
- **Desktop**: Full three-column layout with panels

## 🚀 Modern Angular Features

- **Standalone Components**: No NgModule required
- **Angular Signals**: Reactive state management
- **Computed Values**: Derived state calculations
- **Zoneless Change Detection**: Improved performance
- **Template Syntax**: Modern Angular binding patterns

## 📄 License

This project is for educational and demonstration purposes.

## 🤝 Contributing

Feel free to submit issues and enhancement requests!

---

**Built with ❤️ using Angular 20 and Bootstrap 5**