{"name": "live-medical-quiz-app", "version": "1.0.0", "description": "Live Medical Quiz Application built with Angular", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development"}, "private": true, "dependencies": {"@angular/common": "^20.1.0", "@angular/compiler": "^20.1.0", "@angular/core": "^20.1.0", "@angular/forms": "^20.1.0", "@angular/platform-browser": "^20.1.0", "@angular/router": "^20.1.0", "@popperjs/core": "^2.11.8", "bootstrap": "^5.3.3", "chart.js": "^4.4.6", "ng2-charts": "^6.0.1", "rxjs": "~7.8.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.1.4", "@angular/cli": "^20.1.4", "@angular/compiler-cli": "^20.1.0", "@types/chart.js": "^2.9.41", "typescript": "~5.8.2"}}