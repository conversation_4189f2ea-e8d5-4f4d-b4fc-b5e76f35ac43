/* Custom scrollbar styling to match leaderboard theme */
.questions-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.questions-scroll-container::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
}

.questions-scroll-container::-webkit-scrollbar-thumb {
  background: rgba(255, 215, 0, 0.6);
  border-radius: 3px;
}

.questions-scroll-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 215, 0, 0.8);
}

/* Ensure smooth scrolling */
.questions-scroll-container {
  scroll-behavior: smooth;
}

/* Question button styling enhancements */
.question-btn {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.question-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.question-btn.current {
  border-color: #FFD700;
  box-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.question-btn.correct {
  background-color: #28a745 !important;
  border-color: #1e7e34;
}

.question-btn.incorrect {
  background-color: #dc3545 !important;
  border-color: #bd2130;
}

/* Firefox scrollbar styling */
.questions-scroll-container {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 215, 0, 0.6) rgba(255, 255, 255, 0.1);
}
