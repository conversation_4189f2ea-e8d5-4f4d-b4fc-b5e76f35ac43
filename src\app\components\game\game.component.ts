import { Component, OnInit, OnD<PERSON>roy, signal, computed, inject, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { QUIZ_QUESTIONS, FASTEST_FINGERS, QUESTION_TIMER_DURATION, RESULTS_VIEW_DURATION } from '../../constants';
import { Question, QuestionStatus, ChartData } from '../../types';
import { QuestionsPanelComponent } from '../questions/questions-panel.component';
import { LeaderboardComponent } from '../leaderboard/leaderboard.component';
import { RecentAnswersChartComponent } from '../recent-answers/recent-answers-chart.component';
import { CheckIconComponent } from '../check-icon/check-icon.component';
import { WinnerCelebrationComponent } from '../winner-celebration/winner-celebration.component';
import { AudioService } from '../../services/audio.service';

@Component({
  selector: 'app-game',
  imports: [
    CommonModule,
    QuestionsPanelComponent,
    LeaderboardComponent,
    RecentAnswersChartComponent,
    CheckIconComponent,
    WinnerCelebrationComponent
  ],
  templateUrl: './game.component.html',
  styleUrl: './game.component.css'
})
export class GameComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('ecgVideo') ecgVideo!: ElementRef<HTMLVideoElement>;
  private audioService = inject(AudioService);
  questions = signal<Question[]>([...QUIZ_QUESTIONS]);
  currentQuestionIndex = signal<number>(0);
  score = signal<number>(0);
  gameState = signal<number>(3); // 0: PLAYING, 1: ANSWERED, 2: FINISHED, 3: WAITING_FOR_NEXT
  selectedAnswerIndex = signal<number | null>(null);
  timer = signal<number>(QUESTION_TIMER_DURATION);
  chartData = signal<ChartData[]>([]);
  fastestFingers = signal(FASTEST_FINGERS);
  currentQuestion = computed(() => this.questions()[this.currentQuestionIndex()]);
  QuestionStatus = QuestionStatus;
  waitingForKeyPress = signal<boolean>(false);

  private timerInterval?: number;
  private keyPressListener?: (event: KeyboardEvent) => void;

  ngOnInit() {
    // Start in waiting state for first question
    this.gameState.set(3); // WAITING_FOR_NEXT
    this.waitingForKeyPress.set(true);
    this.setupKeyPressListener();
  }

  ngAfterViewInit() {
    // Ensure video plays after view is initialized
    setTimeout(() => {
      if (this.ecgVideo?.nativeElement) {
        const video = this.ecgVideo.nativeElement;

        // Add event listeners for debugging
        video.addEventListener('loadstart', () => console.log('Video: Load started'));
        video.addEventListener('loadeddata', () => console.log('Video: Data loaded'));
        video.addEventListener('canplay', () => console.log('Video: Can play'));
        video.addEventListener('error', (e) => console.error('Video error:', e));

        video.muted = true; // Start muted for autoplay
        video.load(); // Reload the video

        // Try to play muted first
        video.play().then(() => {
          console.log('Video started playing (muted)');
        }).catch(error => {
          console.error('Video autoplay failed:', error);
        });
      } else {
        console.error('Video element not found');
      }
    }, 100);
  }

  ngOnDestroy() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
    this.audioService.stop('/audios/clock.mp3');
    this.removeKeyPressListener();
  }

  private startTimerAndAudio() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
    this.audioService.stop('/audios/clock.mp3');

    // this.audioService.play('/audios/lets_play.mp3');

    // Start clock ticking after a short delay
    setTimeout(() => {
      this.audioService.play('/audios/clock.mp3');
    }, 1000);

    this.timer.set(QUESTION_TIMER_DURATION);
    this.gameState.set(0);
    this.selectedAnswerIndex.set(null);
    this.timerInterval = window.setInterval(() => {
      const currentTimer = this.timer();
      if (currentTimer <= 1) {
        if (this.gameState() === 0) {
          this.handleSelectAnswer(-1); // Timeout - no answer selected
        } else if (this.gameState() === 1) {
          this.handleNextQuestion();
        }
      } else {
        this.timer.set(currentTimer - 1);
      }
    }, 1000);
  }

  handleNextQuestion() {
    const currentIndex = this.currentQuestionIndex();
    const questionsLength = this.questions().length;
    if (currentIndex < questionsLength - 1) {
      this.currentQuestionIndex.set(currentIndex + 1);
      this.chartData.set([]);
      // Set waiting state instead of starting timer immediately
      this.gameState.set(3); // WAITING_FOR_NEXT
      this.waitingForKeyPress.set(true);
    } else {
      this.gameState.set(2); // FINISHED
      if (this.timerInterval) {
        clearInterval(this.timerInterval);
      }
      this.audioService.stop('/audios/clock.mp3');
    }
  }

  handleSelectAnswer(answerIndex: number) {
    if (this.gameState() !== 0) return;

    // Stop the clock sound
    this.audioService.stop('/audios/clock.mp3');

    const currentQ = this.currentQuestion();

    // Handle timeout case (answerIndex = -1)
    if (answerIndex === -1) {
      // Play timeout sound
      this.audioService.play('/audios/timeout.mp3');

      // Mark as incorrect for timeout
      const updatedQuestions = this.questions().map((q, index) =>
        index === this.currentQuestionIndex()
          ? { ...q, status: QuestionStatus.INCORRECT }
          : q
      );
      this.questions.set(updatedQuestions);
      this.selectedAnswerIndex.set(-1); // No answer selected
    } else {
      // Handle normal answer selection
      const isCorrect = answerIndex === currentQ.correctAnswerIndex;

      // Play appropriate sound based on correctness
      if (isCorrect) {
        this.audioService.play('/audios/correct_answer.mp3');
        this.score.set(this.score() + 10);
      } else {
        this.audioService.play('/audios/wrong_answer.mp3');
      }

      // Update question status
      const updatedQuestions = this.questions().map((q, index) =>
        index === this.currentQuestionIndex()
          ? { ...q, status: isCorrect ? QuestionStatus.CORRECT : QuestionStatus.INCORRECT }
          : q
      );
      this.questions.set(updatedQuestions);
      this.selectedAnswerIndex.set(answerIndex);
    }

    // Generate chart data for results
    let remaining = 100;
    const finalData = currentQ.options.map((option: string, i: number, arr: string[]) => {
      let val;
      if (i === arr.length - 1) {
        val = remaining;
      } else {
        val = Math.floor(Math.random() * (remaining / 2));
        remaining -= val;
      }
      return {
        name: `${String.fromCharCode(65 + i)}. ${option}`,
        value: val,
      };
    }).sort(() => Math.random() - 0.5);

    this.chartData.set(finalData);
    this.gameState.set(1);
    this.timer.set(RESULTS_VIEW_DURATION);
  }

  getAnswerButtonClass(index: number): string {
    const currentQ = this.currentQuestion();
    if (this.gameState() === 1) {
      if (index === currentQ.correctAnswerIndex) {
        return "correct"; // ✅ This matches .answer-btn.correct
      }
      if (index === this.selectedAnswerIndex()) {
        return "incorrect"; // ✅ This matches .answer-btn.incorrect
      }
      return "disabled"; // Optional: Add styling for disabled
    }
    return "";
  }

  fromCharCode(code: number): string {
    return String.fromCharCode(code);
  }

  private setupKeyPressListener() {
    this.keyPressListener = (event: KeyboardEvent) => {
      // Only respond to Tab key when waiting for next question
      if (this.waitingForKeyPress() && this.gameState() === 3 && event.key === 'Tab') {
        event.preventDefault(); // Prevent default tab behavior

        // Ensure video is playing with sound on user interaction
        this.ensureVideoPlaying();

        this.startNextQuestion();
      }
    };
    document.addEventListener('keydown', this.keyPressListener);
  }

  private removeKeyPressListener() {
    if (this.keyPressListener) {
      document.removeEventListener('keydown', this.keyPressListener);
      this.keyPressListener = undefined;
    }
  }

  private startNextQuestion() {
    this.waitingForKeyPress.set(false);
    this.gameState.set(0); // PLAYING
    this.startTimerAndAudio();
  }

  private ensureVideoPlaying() {
    if (this.ecgVideo?.nativeElement) {
      const video = this.ecgVideo.nativeElement;
      console.log('Video state:', {
        paused: video.paused,
        muted: video.muted,
        readyState: video.readyState,
        currentTime: video.currentTime
      });

      if (video.paused) {
        video.muted = false; // Try with sound first
        video.play().catch(error => {
          console.log('Video play with sound failed:', error);
          // Fallback to muted if sound fails
          video.muted = true;
          video.play().catch(e => console.log('Video play failed completely:', e));
        });
      } else if (video.muted) {
        // Video is playing but muted, try to unmute
        video.muted = false;
      }
    }
  }
}
