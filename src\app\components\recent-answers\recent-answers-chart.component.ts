import { Component, Input, OnChanges, SimpleChanges, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { Chart, ChartConfiguration, ChartType, registerables } from 'chart.js';
import { ChartData } from '../../types';

Chart.register(...registerables);

@Component({
  selector: 'app-recent-answers-chart',
  standalone: true,
  templateUrl: './recent-answers-chart.component.html',
})
export class RecentAnswersChartComponent implements OnChanges, AfterViewInit {
  @Input() data: ChartData[] = [];
  @Input() correctAnswerName: string = '';

  @ViewChild('chartCanvas') chartCanvas!: ElementRef<HTMLCanvasElement>;
  private chart?: Chart;

  ngAfterViewInit() {
    this.createChart();
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['data'] && this.chart) {
      this.updateChart();
    }
  }

  private createChart() {
    if (!this.chartCanvas) return;

    const ctx = this.chartCanvas.nativeElement.getContext('2d');
    if (!ctx) return;

    const config: ChartConfiguration = {
      type: 'bar' as ChartType,
      data: {
        labels: this.data.map(item => item.name),
        datasets: [{
          data: this.data.map(item => item.value),
          backgroundColor: this.data.map(item => 
            item.name === this.correctAnswerName ? '#10B981' : '#F87171'
          ),
          borderWidth: 0
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (context: import('chart.js').TooltipItem<'bar'>) => `${context.parsed.y}%`
            }
          }
          
        },
        scales: {
          y: {
            beginAtZero: true,
            max: 50,
            ticks: {
              color: '#4A5568'
            }
          },
          x: {
            ticks: {
              color: '#4A5568'
            }
          }
        }
      }
    };

    this.chart = new Chart(ctx, config);
  }

  private updateChart() {
    if (!this.chart) return;

    this.chart.data.labels = this.data.map(item => item.name);
    this.chart.data.datasets[0].data = this.data.map(item => item.value);
    this.chart.data.datasets[0].backgroundColor = this.data.map(item => 
      item.name === this.correctAnswerName ? '#10B981' : '#F87171'
    );
    this.chart.update();
  }
}